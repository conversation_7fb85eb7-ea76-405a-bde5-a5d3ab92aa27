<?php include '../components/authentication.php';?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <?php include '../components/header.php';
        include '../db_conn.php';
        ?>

        <!-- DataTables CSS -->
        <link rel="stylesheet" media="screen, print" href="../css/datagrid/datatables/datatables.bundle.css">
        
    </head>

    <body class="desktop chrome webkit nav-function-top mod-main-boxed mod-pace-custom mod-panel-icon nav-mobile-push nav-function-fixed header-function-fixed mod-clean-page-bg pace-done mod-bg-3 mod-fixed-bg " >
        <!-- DOC: script to save and load page settings -->
        <script src="../components/top-script.js"></script>
        <!-- BEGIN Page Wrapper -->
        <div class="page-wrapper">
            <div class="page-inner">
                <!-- BEGIN Left Aside -->
                <aside class="page-sidebar">
                    <div class="page-logo">
                        <a href="#" class="page-logo-link press-scale-down d-flex align-items-center position-relative" data-toggle="modal" data-target="#modal-shortcut">
                            <img src="../img/logo.png" alt="SmartAdmin WebApp" aria-roledescription="logo">
                            <span class="page-logo-text mr-1">SmartAdmin WebApp</span>
                            <span class="position-absolute text-white opacity-50 small pos-top pos-right mr-2 mt-n2"></span>
                            <i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>
                        </a>
                    </div>
                    <!-- BEGIN PRIMARY NAVIGATION -->
                   <?php include '../components/primary-nav-admin-products.php';?>
                    <!-- END PRIMARY NAVIGATION -->
                   
                </aside>
                <!-- END Left Aside -->
                <div class="page-content-wrapper">
                    <!-- BEGIN Page Header -->
                    <?php include '../components/page-header.php';?>
                    <!-- END Page Header -->
                    <!-- BEGIN Page Content -->
                    <!-- the #js-page-content id is needed for some plugins to initialize -->
                    <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);">Product</a></li>
                            <li class="breadcrumb-item">Lists</li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"> <?= date ?> </li>
                        </ol>
                    
                        <!-- Your main content goes below here: -->
												 <div class="row">
                            <div class="col-xl-12">
                                <div id="panel-content" class="panel">
																	<div class="p-4">
                                     <!-- datatable start -->
                                    <table id="dt-products" class="table table-bordered table-hover table-sm table-striped w-100 ">
                                        <thead class="bg-primary text-light">
                                            <tr>
                                                <!-- <th style="font-size:12px; width: 30px;"></th> -->
																								<th style="font-size:12px;">Barcode</th>
                                                <th style="font-size:12px;">Category</th>
																								<th style="font-size:12px; width: 100px;">Image</th>
                                                <th style="font-size:12px;">Product name</th>
                                                <th style="font-size:12px;">Product description</th>
                                                <th style="font-size:12px;">Pricing</th>
                                                <th style="font-size:12px;">Quantity</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
																				<!-- datatable end -->
																	</div>
															</div>
														</div>
													</div>
                        <?php include 'modal.php';?>
                    </main>
                    <!-- this overlay is activated only when mobile menu is triggered -->
                    <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div> <!-- END Page Content -->
                    <!-- BEGIN Page Footer -->
                    <?php include '../components/footer.php';?>
                    
                </div>
            </div>
        </div>

       <?php include '../components/bottom.php';?>

       <!-- DataTables JavaScript -->
       <script src="../js/datagrid/datatables/datatables.bundle.js"></script>

       <script>
           $(document).ready(function() {
               // Handle Add Product button click with conditional modal logic
               $('#btn-add-product').on('click', function() {
                   // Check if Add Category modal is currently visible
                   if ($('#modal-add-category').hasClass('show')) {
                       // Hide Add Category modal first, then show Add Product modal
                       $('#modal-add-category').modal('hide');

                       // Wait for the category modal to finish hiding before showing product modal
                       $('#modal-add-category').on('hidden.bs.modal.addProduct', function() {
                           // Remove this specific event listener to avoid multiple bindings
                           $(this).off('hidden.bs.modal.addProduct');
                           // Show Add Product modal
                           $('#modal-add-product').modal('show');
                       });
                   } else {
                       // Add Category modal is not visible, simply show Add Product modal
                       $('#modal-add-product').modal('show');
                   }
               });

               // Handle Add Category button click with conditional modal logic
               $('#btn-add-category').on('click', function() {
                   // Check if Add Product modal is currently visible
                   if ($('#modal-add-product').hasClass('show')) {
                       // Hide Add Product modal first, then show Add Category modal
                       $('#modal-add-product').modal('hide');

                       // Wait for the product modal to finish hiding before showing category modal
                       $('#modal-add-product').on('hidden.bs.modal.addCategory', function() {
                           // Remove this specific event listener to avoid multiple bindings
                           $(this).off('hidden.bs.modal.addCategory');
                           // Show Add Category modal
                           $('#modal-add-category').modal('show');
                       });
                   } else {
                       // Add Product modal is not visible, simply show Add Category modal
                       $('#modal-add-category').modal('show');
                   }
               });

               // Handle Back to Product button click
               $('#btn-back-to-product').on('click', function() {
                   // Hide Add Category modal first, then show Add Product modal
                   $('#modal-add-category').modal('hide');

                   // Wait for the category modal to finish hiding before showing product modal
                   $('#modal-add-category').on('hidden.bs.modal.backToProduct', function() {
                       // Remove this specific event listener to avoid multiple bindings
                       $(this).off('hidden.bs.modal.backToProduct');
                       // Show Add Product modal
                       $('#modal-add-product').modal('show');
                   });
               });

               // Handle Add Category form submission via AJAX
               $('#form-add-category').on('submit', function(e) {
                   e.preventDefault(); // Prevent default form submission

                   // Get the submit button
                   var $submitBtn = $('#btnAddCategory');
                   var originalBtnText = $submitBtn.html();

                   // Disable submit button and show loading state
                   $submitBtn.prop('disabled', true).html('<i class="fal fa-spinner fa-spin"></i> Adding...');

                   // Create FormData object
                   var formData = new FormData(this);

                   // Add store ID from URL parameter if present
                   const urlParams = new URLSearchParams(window.location.search);
                   if (urlParams.get('storeID')) {
                       formData.append('storeID', urlParams.get('storeID'));
                   }

                   // Submit via AJAX
                   $.ajax({
                       url: 'add_category.php',
                       type: 'POST',
                       data: formData,
                       processData: false,
                       contentType: false,
                       dataType: 'json',
                       success: function(response) {
                           // Re-enable submit button
                           $submitBtn.prop('disabled', false).html(originalBtnText);

                           if (response.status === 'success') {
                               // Show success notification
                               toastr.success(response.message, 'Success');

                               // Clear form fields
                               $('#form-add-category')[0].reset();

                               // Refresh category dropdown
                               refreshCategoryDropdown(response.category_id, response.category_name);

                               // Automatically return to Add Product modal
                               $('#modal-add-category').modal('hide');

                               // Wait for category modal to hide before showing product modal
                               $('#modal-add-category').on('hidden.bs.modal.categoryAdded', function() {
                                   $(this).off('hidden.bs.modal.categoryAdded');
                                   $('#modal-add-product').modal('show');
                               });

                           } else {
                               // Show error notification
                               toastr.error(response.message || 'Failed to add category', 'Error');
                           }
                       },
                       error: function(xhr, status, error) {
                           // Re-enable submit button
                           $submitBtn.prop('disabled', false).html(originalBtnText);

                           // Try to parse error response
                           var errorMessage = 'An error occurred while adding the category';

                           try {
                               var response = JSON.parse(xhr.responseText);
                               if (response.message) {
                                   errorMessage = response.message;
                               }
                           } catch (e) {
                               console.error('Error parsing response:', e);
                           }

                           // Show error notification
                           toastr.error(errorMessage, 'Error');

                           // Log error for debugging
                           console.error('AJAX Error:', {
                               status: status,
                               error: error,
                               response: xhr.responseText
                           });
                       }
                   });
               });

               // Function to refresh category dropdown
               function refreshCategoryDropdown(newCategoryId, newCategoryName) {
                   const urlParams = new URLSearchParams(window.location.search);
                   const storeID = urlParams.get('storeID');

                   if (!storeID) {
                       console.error('Store ID not found in URL parameters');
                       return;
                   }

                   // Fetch updated categories from server
                   $.ajax({
                       url: 'ajax/get_categories.php',
                       type: 'POST',
                       data: { storeID: storeID },
                       dataType: 'json',
                       success: function(categories) {
                           // Clear existing options except the first one
                           $('#product_category').find('option:not(:first)').remove();

                           // Add updated categories
                           $.each(categories, function(index, category) {
                               $('#product_category').append(
                                   $('<option></option>').val(category.id).text(category.category)
                               );
                           });

                           // Select the newly added category
                           if (newCategoryId) {
                               $('#product_category').val(newCategoryId);
                           }
                       },
                       error: function(xhr, status, error) {
                           console.error('Failed to refresh categories:', error);
                           // Fallback: manually add the new category
                           if (newCategoryId && newCategoryName) {
                               $('#product_category').append(
                                   $('<option></option>').val(newCategoryId).text(newCategoryName)
                               );
                               $('#product_category').val(newCategoryId);
                           }
                       }
                   });
               }

               // Initialize DataTables with AJAX
               $('#dt-products').DataTable({
                   processing: true,
                   serverSide: true,
                   ajax: {
                       url: 'ajax/get_products.php',
                       type: 'POST',
                       data: function(d) {
                           // Pass URL parameter to DataTables AJAX request
                           const urlParams = new URLSearchParams(window.location.search);
                           if (urlParams.get('storeID')) {
                               d.storeID = urlParams.get('storeID');
                           }
                           return d;
                       },
                       error: function(xhr, error, code) {
                           console.error('DataTables AJAX error:', error);
                           if (window.showToast) {
                               window.showToast('error', 'Failed to load products data. Please try again.');
                           }
                       }
                   },
                   columns: [
									 {
                           title: 'Barcode',
                           data: 0,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
                       {
                           title: 'Category',
                           data: 1,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
											 {
                           title: 'Image',
                           data: 2,
                           orderable: false,
                           searchable: false,
                           className: 'text-center p-2'
                       },
                       {
                           title: 'Product Name',
                           data: 3,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
                       {
                           title: 'Product Description',
                           data: 4,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
                       {
                           title: 'Pricing',
                           data: 5,
                           orderable: true,
                           searchable: true,
                           className: 'text-right p-2'
                       },
                       {
                           title: 'Quantity',
                           data: 6,
                           orderable: true,
                           searchable: true,
                           className: 'text-center p-2'
                       }
                   ],
                   order: [[0, 'asc']], // Default sort by category name
                   pageLength: 25,
                   lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                   language: {
                       processing: "Loading products...",
                       emptyTable: "No products found",
                       zeroRecords: "No matching products found"
                   },
                   responsive: true,
                   autoWidth: false,
                   stateSave: true, // Save table state (pagination, sorting, etc.)
                   dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 text-right"f>>' +
                        '<"row"<"col-sm-12"tr>>' +
                        '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
               });

           });

					 //Query product with product code
					 function queryCredentialsAndPopulateModal(rowData) {
        // Extract the product code information
        var productcode = rowData.product_code;

        console.log('Row data:', rowData);
        console.log('Product Code:', productcode);

        // Show loading indicator
        //toastr.info('Loading product credentials...', 'Please wait');

        // Query the product credentials via AJAX
        $.ajax({
            url: 'ajax/fetch_product.php',
            type: 'POST',
            dataType: 'json',
            data: function() {
                // Build data object
                var ajaxData = {
                    action: 'query_product_credentials',
                    productcode: productcode
                };

                // Pass URL parameter to AJAX requests if present
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('storeID')) {
                    ajaxData.storeID = urlParams.get('storeID');
                }

                return ajaxData;
            }(),
            success: function(response) {
                console.log('Raw response:', response);
                console.log('Response type:', typeof response);

                // Check if response is already an object (jQuery auto-parsed)
                if (typeof response === 'object') {
                    console.log('Response is already an object:', response);

                    if (response.success) {
                        // Populate the modal with the product credentials
                        populateLinkSetModal(response.data);

                        // Show the modal
                        $('#modal-add-product').modal('show');

                        //toastr.success('Product credentials loaded on the modal');
                    } else {
                        console.error('Server error:', response.message);
                        toastr.error(response.message || 'Failed to load product credentials');

                        // Show debug info if available
                        if (response.debug) {
                            console.error('Debug info:', response.debug);
                        }
                    }
                } else {
                    // Try to parse as JSON
                    try {
                        const result = JSON.parse(response);
                        console.log('Parsed result:', result);

                        if (result.success) {
                            // Populate the modal with the credentials
                            populateLinkSetModal(result.data);

                            // Show the modal
                            $('#modal-add-product').modal('show');

                            //toastr.success('Product credentials loaded on the modal');
                        } else {
                            console.error('Server error:', result.message);
                            toastr.error(result.message || 'Failed to load product credentials');

                            // Show debug info if available
                            if (result.debug) {
                                console.error('Debug info:', result.debug);
                            }
                        }
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response was:', response);
                        console.error('Response length:', response.length);
                        console.error('First 500 chars:', response.substring(0, 500));
                        toastr.error('Invalid server response. Check console for details.');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                console.error('Response text:', xhr.responseText);
                toastr.error('Server error occurred while loading product credentials: ' + error);
            }
        });
    }
       </script>

			<!-- Enhanced Product Search Component -->
			<script src="auto_search_component.js"></script>
			<script>
			$(document).ready(function() {
			    // Initialize enhanced product search in modal
			    let modalSearch = null;

			    // Initialize search when modal is shown
			    $('#modal-add-product').on('shown.bs.modal', function() {
			        if (!modalSearch) {
			            modalSearch = new AutoProductSearch('#modalProductSearch', {
			                delay: 400,
			                minLength: 2,
			                endpoint: 'ajax/fetch_product.php',
			                onSearchStart: function(searchTerm) {
			                    $('#modalSearchStatus').show();
			                    $('#modalSearchStatusText').html('<i class="fal fa-spinner fa-spin"></i> Searching for "' + searchTerm + '"...');
			                },
			                onResults: function(data) {
			                    displayModalSearchResults(data);
			                },
			                onError: function(error) {
			                    $('#modalSearchStatus').show();
			                    $('#modalSearchStatusText').html('<i class="fal fa-exclamation-triangle text-danger"></i> Search error: ' + error.message);
			                    $('#modalSearchResults').hide();
			                },
			                onSearchEnd: function(searchTerm) {
			                    // Search completed
			                }
			            });

			            // Override the AJAX data to include storeID
			            const originalPerformSearch = modalSearch.performSearch;
			            modalSearch.performSearch = function() {
			                const searchTerm = this.input.val().trim();

			                if (searchTerm === '' || searchTerm.length < this.options.minLength) {
			                    return;
			                }

			                // Prevent multiple simultaneous searches
			                if (this.isSearching) {
			                    return;
			                }

			                this.isSearching = true;

			                // Trigger search start callback
			                if (this.options.onSearchStart) {
			                    this.options.onSearchStart(searchTerm);
			                }

			                // Show loading if enabled
			                if (this.options.showLoading && this.options.onResults) {
			                    this.options.onResults({
			                        loading: true,
			                        search_term: searchTerm
			                    });
			                }

			                // Get storeID from URL
			                const urlParams = new URLSearchParams(window.location.search);
			                const storeID = urlParams.get('storeID');

			                // Perform AJAX search with storeID
			                $.ajax({
			                    url: this.options.endpoint,
			                    type: 'POST',
			                    dataType: 'json',
			                    data: {
			                        action: 'search_products',
			                        search_term: searchTerm,
			                        storeID: storeID
			                    },
			                    success: (response) => {
			                        this.handleSearchSuccess(response, searchTerm);
			                    },
			                    error: (xhr, status, error) => {
			                        this.handleSearchError(xhr, status, error, searchTerm);
			                    },
			                    complete: () => {
			                        this.isSearching = false;

			                        // Trigger search end callback
			                        if (this.options.onSearchEnd) {
			                            this.options.onSearchEnd(searchTerm);
			                        }
			                    }
			                });
			            };
			        }
			    });

			    // Clear search button in modal
			    $('#modalClearSearchBtn').click(function() {
			        $('#modalProductSearch').val('').trigger('input');
			        $('#modalSearchResults').hide();
			        $('#modalSearchStatus').hide();
			    });

			    // Reset search when modal is hidden
			    $('#modal-add-product').on('hidden.bs.modal', function() {
			        $('#modalProductSearch').val('');
			        $('#modalSearchResults').hide();
			        $('#modalSearchStatus').hide();

			        // Reset image preview
			        $('#productImagePreview').attr('src', '../img_products/unknown-product.png');
			        $('#productImageName').val('No image selected');
			        $('#existingProductImage').remove();
			    });

			    // Handle manual file selection
			    $('#product_image').on('change', function() {
			        const file = this.files[0];
			        if (file) {
			            // Show preview of selected file
			            const reader = new FileReader();
			            reader.onload = function(e) {
			                $('#productImagePreview').attr('src', e.target.result);
			                $('#productImageName').val(file.name);
			            };
			            reader.readAsDataURL(file);

			            // Remove existing image reference since user selected new file
			            $('#existingProductImage').remove();
			        } else {
			            // No file selected, reset to default
			            $('#productImagePreview').attr('src', '../img_products/unknown-product.png');
			            $('#productImageName').val('No image selected');
			        }
			    });

			    function displayModalSearchResults(data) {
			        if (data.loading) {
			            return; // Loading handled in onSearchStart
			        }

			        if (data.error) {
			            $('#modalSearchStatus').show();
			            $('#modalSearchStatusText').html('<i class="fal fa-exclamation-triangle text-danger"></i> ' + data.error_message);
			            $('#modalSearchResults').hide();
			            return;
			        }

			        if (data.cleared || !data.id) {
			            $('#modalSearchResults').hide();
			            $('#modalSearchStatus').hide();
			            return;
			        }

			        // Update status with validation information
			        $('#modalSearchStatus').show();
			        if (data.exists_in_current_store) {
			            $('#modalSearchStatusText').html('<i class="fal fa-exclamation-triangle text-warning"></i> Found product for "' + data.search_term + '" - ' + data.validation_message);
			        } else {
			            $('#modalSearchStatusText').html('<i class="fal fa-check-circle text-success"></i> Found product for "' + data.search_term + '"');
			        }

			        // Display single product result (data is now the product object directly)
			        const product = data;
			        let html = '<div class="list-group">';
			        html += `
			            <div class="list-group-item list-group-item-action modal-product-item" data-product-id="${product.id}" style="cursor: pointer;">
			                <div class="d-flex w-100 justify-content-between align-items-start">
												<div class="img">
															<img src="../img_products/${product.product_image}" alt="${product.product_name}" width="50">
															</div>
			                    <div class="flex-grow-1">
			                        <div class="d-flex align-items-center mb-1">
			                            <span class="badge badge-primary mr-2">${product.product_code}</span>
			                            <h6 class="mb-0">${product.product_name}</h6>
			                        </div>
			                        <p class="mb-1 text-muted small">${product.product_description}</p>
			                        <div class="d-flex justify-content-between align-items-center">
			                            <small class="text-success font-weight-bold">$${product.selling_price.toFixed(2)}</small>
			                        </div>
			                        ${product.exists_in_current_store ?
			                            '<div class="mt-2"><small class="text-warning"><i class="fal fa-exclamation-triangle"></i> ' + product.validation_message + '</small></div>' :
			                            ''
			                        }
			                    </div>
			                    <div class="ml-2">
			                        ${product.can_add_to_store ?
			                            '<button type="button" class="btn btn-sm btn-outline-primary btn-populate-form" data-product-id="' + product.id + '"><i class="fal fa-arrow-down"></i> Use</button>' :
			                            '<button type="button" class="btn btn-sm btn-outline-secondary" disabled title="' + product.validation_message + '"><i class="fal fa-ban"></i> Already Added</button>'
			                        }
			                    </div>
			                </div>
			            </div>
			        `;
			        html += '</div>';

			        $('#modalSearchResultsContainer').html(html);
			        $('#modalSearchResults').show();

			        // Handle product selection to populate form (only if can add to store)
			        $('.modal-product-item').click(function(e) {
			            // Don't trigger if clicking on button
			            if ($(e.target).closest('.btn').length > 0) {
			                return;
			            }

			            // Only allow if product can be added to store
			            if (product.can_add_to_store) {
			                populateFormWithProduct(product);
			            }
			        });

			        // Handle populate button (only for enabled buttons)
			        $('.btn-populate-form').click(function(e) {
			            e.stopPropagation();
			            // Only allow if product can be added to store
			            if (product.can_add_to_store) {
			                populateFormWithProduct(product);
			            }
			        });
			    }

			    function populateFormWithProduct(product) {
			        // Populate the form fields with selected product data
			        $('#inputName').val(product.product_name);
			        $('#inputDescription').val(product.product_description);
			        $('#inputPrice').val(product.selling_price);
			        $('#productCode').val(product.product_code);

			        // Set category if it exists
			        if (product.category_id) {
			            $('#product_category').val(product.category_id);
			        }

			        // Handle product image
			        if (product.product_image && product.product_image !== '') {
			            // Update image preview
			            const imagePath = '../img_products/' + product.product_image;
			            $('#productImagePreview').attr('src', imagePath);
			            $('#productImageName').val(product.product_image);

			            // Clear the file input since we're using existing image
			            $('#product_image').val('');

			            // Add hidden field to track existing image
			            $('#existingProductImage').remove(); // Remove if exists
			            $('<input>').attr({
			                type: 'hidden',
			                id: 'existingProductImage',
			                name: 'existing_product_image',
			                value: product.product_image
			            }).appendTo('#modal-add-product form');
			        } else {
			            // No image available
			            $('#productImagePreview').attr('src', '../img_products/unknown-product.png');
			            $('#productImageName').val('No image available');
			            $('#product_image').val('');
			            $('#existingProductImage').remove();
			        }

			        // Highlight the selected item
			        $('.modal-product-item').removeClass('active');
			        $('[data-product-id="' + product.id + '"]').addClass('active');

			        // Show success message
			        if (typeof toastr !== 'undefined') {
			            toastr.success(product.product_name, 'Product Selected');
			        }

			        // Hide search results after selection
			        $('#modalSearchResults').hide();
			        $('#modalSearchStatus').hide();
			        $('#modalProductSearch').val('');


			    }


			});
			</script>

			<!-- Enhanced Search Styles -->
			<style>
			.modal-product-item {
			    transition: all 0.2s ease;
			    border-left: 3px solid transparent;
			}

			.modal-product-item:hover {
			    background-color: #f8f9fa;
			    border-left-color: #007bff;
			}

			.modal-product-item.active {
			    background-color: #e7f3ff;
			    border-left-color: #007bff;
			    border-color: #007bff;
			}

			#modalSearchResults {
			    border: 1px solid #dee2e6;
			    border-radius: 0.375rem;
			    background-color: #fff;
			}

			.list-group-item:first-child {
			    border-top-left-radius: 0.375rem;
			    border-top-right-radius: 0.375rem;
			}

			.list-group-item:last-child {
			    border-bottom-left-radius: 0.375rem;
			    border-bottom-right-radius: 0.375rem;
			}

			.btn-populate-form {
			    min-width: 60px;
			}

			#modalProductSearch:focus {
			    border-color: #007bff;
			    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
			}
			</style>

			<script src="../assets/js/zoom_image.js"></script>
    </body>
    <!-- END Body -->
</html>
<script>
$(document).ready(function() {
    // Handle form submission via AJAX
    $('#modal-add-product form').on('submit', function(e) {
        e.preventDefault(); // Prevent default form submission

        // Get the submit button
        var $submitBtn = $('#btnAddProduct');
        var originalBtnText = $submitBtn.html();

        // Disable submit button and show loading state
        $submitBtn.prop('disabled', true).html('<i class="fal fa-spinner fa-spin"></i> Adding...');

        // Create FormData object to handle file upload
        var formData = new FormData(this);

        // Submit via AJAX
        $.ajax({
            url: 'add_product.php',
            type: 'POST',
            data: formData,
            processData: false, // Don't process the data
            contentType: false, // Don't set content type (let browser set it with boundary)
            dataType: 'json',
            success: function(response) {
                // Re-enable submit button
                $submitBtn.prop('disabled', false).html(originalBtnText);

                if (response.status === 'success') {
                    // Show success notification
                    toastr.success(response.message, 'Success');

                    // Get the currently selected category value
                    var selectedCategory = $('#product_category').val();

                    // Clear form fields
                    $('#inputName').val('');
                    $('#inputDescription').val('');
                    $('#inputPrice').val('');
                    $('#productCode').val('');

                    // Keep the selected category (don't reset it)
                    // $('#product_category').val(''); // DON'T clear category

                    // Reset image preview to default
                    $('#productImagePreview').attr('src', '<?= base_url . 'img_products/unknown-product.png'?>');
                    $('#productImageName').val('No image selected');

                    // Clear file input
                    $('#product_image').val('');

                    // Remove existing product image hidden field
                    $('#existingProductImage').remove();

                    // Reload DataTable to show new product
                    if ($.fn.DataTable.isDataTable('#dt-products')) {
                        $('#dt-products').DataTable().ajax.reload(null, false);
                    }

                    // Keep modal open for quick multiple entries
                    // $('#modal-add-product').modal('hide'); // DON'T close modal

                } else {
                    // Show error notification
                    toastr.error(response.message || 'Failed to add product', 'Error');
                }
            },
            error: function(xhr, status, error) {
                // Re-enable submit button
                $submitBtn.prop('disabled', false).html(originalBtnText);

                // Try to parse error response
                var errorMessage = 'An error occurred while adding the product';

                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    // If parsing fails, use default error message
                    console.error('Error parsing response:', e);
                }

                // Show error notification
                toastr.error(errorMessage, 'Error');

                // Log error for debugging
                console.error('AJAX Error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
            }
        });
    });
});
</script>