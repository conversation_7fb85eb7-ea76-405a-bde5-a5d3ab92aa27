<?php
require_once __DIR__ . '/../../db_conn.php';

// Fetch product by product code
if (isset($_POST['action']) && $_POST['action'] === 'query_product_credentials') {
    header('Content-Type: application/json');

    try {
        if (!isset($conn)) {
            echo json_encode(['success' => false, 'message' => 'Database connection not available']);
            exit;
        }

        $product_code = trim($_POST['productcode'] ?? '');

        if (empty($product_code)) {
            echo json_encode(['success' => false, 'message' => 'Product code is required']);
            exit;
        }

        $stmt = $conn->prepare("SELECT id, product_name, product_description, product_code, product_image, selling_price, product_qty
                               FROM store_products
                               WHERE product_code = ?
                               LIMIT 1");
        $stmt->bind_param('s', $product_code);
        $stmt->execute();
        $result = $stmt->get_result();
        $product = $result->fetch_assoc();

        if (!$product) {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }

        echo json_encode(['success' => true, 'data' => $product]);

    } catch (Exception $e) {
        error_log("Query product error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }

    exit;
}

// Search products functionality
if (isset($_POST['action']) && $_POST['action'] === 'search_products') {
    header('Content-Type: application/json');

    try {
        if (!isset($conn)) {
            echo json_encode(['success' => false, 'message' => 'Database connection not available']);
            exit;
        }

        $search_term = trim($_POST['search_term'] ?? '');
        $current_store_id = isset($_POST['storeID']) ? intval(trim($_POST['storeID'])) : 0;

        if (empty($search_term)) {
            echo json_encode(['success' => false, 'message' => 'Search term is required']);
            exit;
        }

        // Validate store ID if provided
        if ($current_store_id <= 0) {
            // If no valid store ID, search globally but indicate no store context
            $current_store_id = null;
        }

        // Search query with exact product_code matching only
        $query = "SELECT id, store_id, product_name, product_description, product_code, product_image, selling_price, product_qty
                  FROM store_products
                  WHERE product_code = ?
                  LIMIT 1";

        $stmt = $conn->prepare($query);
        $stmt->bind_param('s', $search_term);
        $stmt->execute();
        $result = $stmt->get_result();

        $product = $result->fetch_assoc();

        if (!$product) {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }

        // Check if product exists in current store (barcode uniqueness validation)
        $exists_in_current_store = false;
        $validation_message = '';

        if ($current_store_id !== null) {
            // Check if this barcode already exists in the current store
            $check_query = "SELECT id FROM store_products WHERE product_code = ? AND store_id = ? LIMIT 1";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param('si', $search_term, $current_store_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->fetch_assoc()) {
                $exists_in_current_store = true;
                $validation_message = 'This product is already added to your store';
            }
        }

        $response = [
            'success' => true,
            'data' => [
                'id' => intval($product['id']),
                'product_code' => htmlspecialchars($product['product_code'] ?? ''),
                'product_name' => htmlspecialchars($product['product_name']),
                'product_description' => htmlspecialchars($product['product_description'] ?? ''),
                'selling_price' => floatval($product['selling_price'] ?? 0),
                'product_qty' => intval($product['product_qty'] ?? 0),
                'product_image' => htmlspecialchars($product['product_image'] ?? ''),
                'search_term' => htmlspecialchars($search_term),
                'exists_in_current_store' => $exists_in_current_store,
                'validation_message' => $validation_message,
                'can_add_to_store' => !$exists_in_current_store
            ]
        ];

        echo json_encode($response);

    } catch (Exception $e) {
        error_log("Product search error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }

    exit;
}