<?php
require_once __DIR__ . '/../db_conn.php';
require_once __DIR__ . '/../utils/import_logger.php';

// Set content type to JSON for AJAX response
header('Content-Type: application/json');

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
    exit;
}

try {
    // Validate required fields
    if (empty($_POST['product_name'])) {
        throw new Exception('Product name is required');
    }
    
    if (empty($_POST['selling_price'])) {
        throw new Exception('Selling price is required');
    }
    
    if (empty($_POST['product_category'])) {
        throw new Exception('Product category is required');
    }
    
    if (empty($_POST['store_owner'])) {
        throw new Exception('Store owner is required');
    }
    
    // Sanitize inputs
    $product_name = trim($_POST['product_name']);
    $product_description = trim($_POST['product_description'] ?? '');
    $selling_price = floatval($_POST['selling_price']);
    $product_code = trim($_POST['product_code'] ?? '');
    $category_id = intval($_POST['product_category']);
    $store_owner = intval($_POST['store_owner']);
    
    // Get store_id from the category (since category belongs to a store)
    $stmt = $conn->prepare("SELECT store_id FROM category WHERE id = ?");
    $stmt->bind_param('i', $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $category = $result->fetch_assoc();
    
    if (!$category) {
        throw new Exception('Invalid category selected');
    }
    
    $store_id = $category['store_id'];

    // Validate barcode uniqueness within the store
    if (!empty($product_code)) {
        $barcode_check_stmt = $conn->prepare("SELECT id FROM store_products WHERE product_code = ? AND store_id = ? LIMIT 1");
        $barcode_check_stmt->bind_param('si', $product_code, $store_id);
        $barcode_check_stmt->execute();
        $barcode_result = $barcode_check_stmt->get_result();

        if ($barcode_result->fetch_assoc()) {
            throw new Exception('This barcode already exists in your store. Each product must have a unique barcode within the store.');
        }
    }

    // Handle image upload
    $product_image = null;
    
    // Check if user selected a new file
    if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] === UPLOAD_ERR_OK) {
        // New file uploaded
        $file = $_FILES['product_image'];
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        
        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('Invalid file type. Only JPG, PNG, and GIF images are allowed');
        }
        
        // Check file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('File size too large. Maximum size is 5MB');
        }
        
        // Generate unique filename
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $new_filename = uniqid('product_', true) . '.' . $file_extension;
        
        // Upload directory
        $upload_dir = __DIR__ . '/../img_products/';
        
        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $upload_path = $upload_dir . $new_filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
            throw new Exception('Failed to upload image');
        }
        
        $product_image = $new_filename;
        
    } elseif (!empty($_POST['existing_product_image'])) {
        // Use existing image (when populating from search)
        $product_image = trim($_POST['existing_product_image']);
    }
    
    // Insert product into database
    $sql = "INSERT INTO store_products 
            (store_id, category_id, product_name, product_description, product_code, product_image, selling_price, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        'iissssd',
        $store_id,
        $category_id,
        $product_name,
        $product_description,
        $product_code,
        $product_image,
        $selling_price
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to insert product: ' . $stmt->error);
    }
    
    $product_id = $stmt->insert_id;

    // Log product creation audit trail
    $audit_success = logProductAudit(
        $conn,
        $store_id,
        'created',
        $product_id,
        $product_name,
        $product_code,
        [
            'category_id' => $category_id,
            'selling_price' => $selling_price,
            'has_image' => !empty($product_image),
            'image_filename' => $product_image
        ]
    );

    if (!$audit_success) {
        // Log the error but don't fail the main operation
        error_log("Failed to log product creation audit for product ID: {$product_id}");
    }

    // Success response
    echo json_encode([
        'status' => 'success',
        'message' => 'Product added successfully!',
        'data' => [
            'product_id' => $product_id,
            'product_name' => $product_name,
            'product_image' => $product_image
        ]
    ]);
    
} catch (Exception $e) {
    // Error response
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}

$conn->close();

