<?php
/**
 * Test script for Product Audit Logging
 * 
 * This script can be used to verify that the audit logging system is working correctly.
 * It should be run in a development environment only.
 * 
 * Usage: Access this file via browser after adding a product through the normal interface
 * to verify that audit logs are being created properly.
 */

require_once __DIR__ . '/../db_conn.php';
require_once __DIR__ . '/../utils/import_logger.php';

// Set content type to HTML for better readability
header('Content-Type: text/html; charset=utf-8');

// Check if user is authenticated
if (!isset($_SESSION['auth_user'])) {
    die('<h1>Access Denied</h1><p>Please log in to access this test script.</p>');
}

$current_user = getCurrentUser();
$store_id = $_SESSION['auth_user']['store_id'] ?? 1; // Default to store 1 for testing

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Audit Logging Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .json { background-color: #f8f8f8; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Product Audit Logging Test</h1>
    
    <div class="info">
        <strong>Current User:</strong> <?= htmlspecialchars($current_user) ?><br>
        <strong>Store ID:</strong> <?= htmlspecialchars($store_id) ?><br>
        <strong>Test Time:</strong> <?= date('Y-m-d H:i:s') ?>
    </div>

    <h2>Recent Product Audit Logs</h2>
    
    <?php
    try {
        // Query recent product audit logs for the current store
        $sql = "SELECT id, store_id, log_type, page_title, column_barcode, column_item, column_qty, log_user, log_datetime 
                FROM logs_audit 
                WHERE store_id = ? AND log_type = 'product_management'
                ORDER BY log_datetime DESC 
                LIMIT 10";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('Failed to prepare query: ' . $conn->error);
        }
        
        $stmt->bind_param('i', $store_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo '<table>';
            echo '<tr><th>ID</th><th>Store ID</th><th>Page Title</th><th>Barcode</th><th>Product Name</th><th>Details</th><th>User</th><th>Date/Time</th></tr>';
            
            while ($row = $result->fetch_assoc()) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['store_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['page_title']) . '</td>';
                echo '<td>' . htmlspecialchars($row['column_barcode']) . '</td>';
                echo '<td>' . htmlspecialchars($row['column_item']) . '</td>';
                echo '<td><div class="json">' . htmlspecialchars($row['column_qty']) . '</div></td>';
                echo '<td>' . htmlspecialchars($row['log_user']) . '</td>';
                echo '<td>' . htmlspecialchars($row['log_datetime']) . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
            echo '<p class="success">✅ Found ' . $result->num_rows . ' recent product audit log(s).</p>';
        } else {
            echo '<p class="info">ℹ️ No product audit logs found for store ID ' . $store_id . '. Try adding a product first.</p>';
        }
        
        $stmt->close();
        
    } catch (Exception $e) {
        echo '<p class="error">❌ Error querying audit logs: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>

    <h2>Test Product Audit Function</h2>
    
    <?php
    if (isset($_POST['test_audit'])) {
        try {
            // Test the logProductAudit function
            $test_success = logProductAudit(
                $conn,
                $store_id,
                'test_action',
                999999, // Test product ID
                'Test Product for Audit Logging',
                'TEST123',
                [
                    'test_field' => 'test_value',
                    'test_timestamp' => date('Y-m-d H:i:s'),
                    'test_user' => $current_user
                ]
            );
            
            if ($test_success) {
                echo '<p class="success">✅ Test audit log created successfully!</p>';
                echo '<p><em>Refresh the page to see the new test log entry above.</em></p>';
            } else {
                echo '<p class="error">❌ Failed to create test audit log.</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">❌ Exception during test: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }
    ?>
    
    <form method="POST">
        <button type="submit" name="test_audit" style="padding: 10px 20px; background-color: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Create Test Audit Log
        </button>
    </form>
    
    <h2>Instructions</h2>
    <ol>
        <li><strong>Add a Product:</strong> Go to the Products page and add a new product using the normal interface.</li>
        <li><strong>Check Logs:</strong> Refresh this page to see if the audit log was created.</li>
        <li><strong>Test Function:</strong> Click the "Create Test Audit Log" button to test the logging function directly.</li>
        <li><strong>Verify Data:</strong> Check that all fields are populated correctly, especially the JSON details.</li>
    </ol>
    
    <h2>Expected Audit Log Structure</h2>
    <div class="json">{
    "action": "created",
    "product_id": 123,
    "timestamp": "2025-10-05 14:30:15",
    "category_id": 3,
    "selling_price": 25.99,
    "has_image": true,
    "image_filename": "product_abc123.jpg"
}</div>

    <p><strong>Note:</strong> This test script should only be used in development environments. Remove or secure it before deploying to production.</p>
    
    <hr>
    <p><small>Product Audit Logging Test Script - Generated on <?= date('Y-m-d H:i:s') ?></small></p>
</body>
</html>
