<?php
/**
 * Import Logging Utility
 * 
 * Provides comprehensive logging functionality for import operations
 * including high-level import metadata and row-level audit trails.
 */

require_once __DIR__ . '/../db_conn.php';

class ImportLogger {
    private $conn;
    private $import_log_id = null;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
    }
    
    /**
     * Log the start of an import operation
     * 
     * @param int $store_id The store ID for multi-tenant security
     * @param string $log_type Type of import (e.g., 'inventory_import')
     * @param string $page_title Page/module where import occurred
     * @param string $file_name Name of the imported file
     * @param string $log_user Username of the user performing the import
     * @return int|false The import log ID on success, false on failure
     */
    public function logImportStart($store_id, $log_type, $page_title, $file_name, $log_user) {
        try {
            $sql = "INSERT INTO logs_import (store_id, log_type, page_title, file_name, log_status, log_user, log_datetime) 
                    VALUES (?, ?, ?, ?, 'in_progress', ?, NOW())";
            
            $stmt = $this->conn->prepare($sql);
            if (!$stmt) {
                error_log("Failed to prepare import log statement: " . $this->conn->error);
                return false;
            }
            
            $stmt->bind_param('issss', $store_id, $log_type, $page_title, $file_name, $log_user);
            
            if ($stmt->execute()) {
                $this->import_log_id = $this->conn->insert_id;
                $stmt->close();
                return $this->import_log_id;
            } else {
                error_log("Failed to execute import log statement: " . $stmt->error);
                $stmt->close();
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception in logImportStart: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update the import log with final status and statistics
     * 
     * @param int $import_log_id The import log ID to update
     * @param string $final_status Final status (success, error, partial_success, etc.)
     * @param array $stats Optional statistics array with keys like total_rows, processed, updated, etc.
     * @return bool Success status
     */
    public function logImportEnd($import_log_id, $final_status, $stats = []) {
        try {
            // Build the status message with statistics if provided
            $status_message = $final_status;
            if (!empty($stats)) {
                $status_parts = [$final_status];
                if (isset($stats['total_rows'])) $status_parts[] = "total_rows:{$stats['total_rows']}";
                if (isset($stats['processed'])) $status_parts[] = "processed:{$stats['processed']}";
                if (isset($stats['updated'])) $status_parts[] = "updated:{$stats['updated']}";
                if (isset($stats['skipped'])) $status_parts[] = "skipped:{$stats['skipped']}";
                if (isset($stats['error_count'])) $status_parts[] = "errors:{$stats['error_count']}";
                if (isset($stats['warning_count'])) $status_parts[] = "warnings:{$stats['warning_count']}";
                
                $status_message = implode('|', $status_parts);
            }
            
            $sql = "UPDATE logs_import SET log_status = ? WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            
            if (!$stmt) {
                error_log("Failed to prepare import log update statement: " . $this->conn->error);
                return false;
            }
            
            $stmt->bind_param('si', $status_message, $import_log_id);
            
            if ($stmt->execute()) {
                $stmt->close();
                return true;
            } else {
                error_log("Failed to execute import log update statement: " . $stmt->error);
                $stmt->close();
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception in logImportEnd: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log individual row processing details for audit trail
     * 
     * @param int $store_id The store ID for multi-tenant security
     * @param string $log_type Type of audit log (e.g., 'inventory_row_processing')
     * @param string $page_title Page/module where processing occurred
     * @param string $barcode Product barcode/code being processed
     * @param string $item_name Product name being processed
     * @param string $quantity Quantity value being processed
     * @param string $processing_status Status of this row (success, failed, skipped, duplicate, etc.)
     * @param string $log_user Username of the user performing the import
     * @return bool Success status
     */
    public function logAuditRow($store_id, $log_type, $page_title, $barcode, $item_name, $quantity, $processing_status, $log_user) {
        try {
            // Create a details string that includes the processing status
            $details = $processing_status;
            
            $sql = "INSERT INTO logs_audit (store_id, log_type, page_title, column_barcode, column_item, column_qty, log_user, log_datetime) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->conn->prepare($sql);
            if (!$stmt) {
                error_log("Failed to prepare audit log statement: " . $this->conn->error);
                return false;
            }
            
            // Format the quantity field to include both the quantity value and processing status
            $qty_with_status = $quantity . '|' . $details;
            
            $stmt->bind_param('issssss', $store_id, $log_type, $page_title, $barcode, $item_name, $qty_with_status, $log_user);
            
            if ($stmt->execute()) {
                $stmt->close();
                return true;
            } else {
                error_log("Failed to execute audit log statement: " . $stmt->error);
                $stmt->close();
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception in logAuditRow: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get the current import log ID
     * 
     * @return int|null The current import log ID or null if not set
     */
    public function getCurrentImportLogId() {
        return $this->import_log_id;
    }
    
    /**
     * Batch log multiple audit rows for better performance
     * 
     * @param array $rows Array of row data, each containing store_id, log_type, page_title, barcode, item_name, quantity, processing_status, log_user
     * @return bool Success status
     */
    public function logAuditRowsBatch($rows) {
        if (empty($rows)) {
            return true;
        }
        
        try {
            // Start transaction for batch insert
            $this->conn->autocommit(false);
            
            $sql = "INSERT INTO logs_audit (store_id, log_type, page_title, column_barcode, column_item, column_qty, log_user, log_datetime) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->conn->prepare($sql);
            if (!$stmt) {
                error_log("Failed to prepare batch audit log statement: " . $this->conn->error);
                $this->conn->rollback();
                $this->conn->autocommit(true);
                return false;
            }
            
            $success_count = 0;
            foreach ($rows as $row) {
                $qty_with_status = $row['quantity'] . '|' . $row['processing_status'];
                
                $stmt->bind_param('issssss', 
                    $row['store_id'], 
                    $row['log_type'], 
                    $row['page_title'], 
                    $row['barcode'], 
                    $row['item_name'], 
                    $qty_with_status, 
                    $row['log_user']
                );
                
                if ($stmt->execute()) {
                    $success_count++;
                } else {
                    error_log("Failed to execute batch audit log for row: " . $stmt->error);
                }
            }
            
            $stmt->close();
            
            // Commit if at least some rows succeeded
            if ($success_count > 0) {
                $this->conn->commit();
                $this->conn->autocommit(true);
                return true;
            } else {
                $this->conn->rollback();
                $this->conn->autocommit(true);
                return false;
            }
            
        } catch (Exception $e) {
            error_log("Exception in logAuditRowsBatch: " . $e->getMessage());
            $this->conn->rollback();
            $this->conn->autocommit(true);
            return false;
        }
    }
}

/**
 * Helper function to get the current user for logging
 *
 * @return string The current user's identifier or 'system' if not available
 */
function getCurrentUser() {
    if (isset($_SESSION['auth_user']['sys_fullname']) && !empty($_SESSION['auth_user']['sys_fullname'])) {
        return trim($_SESSION['auth_user']['sys_fullname']);
    } elseif (isset($_SESSION['auth_user']['sys_email']) && !empty($_SESSION['auth_user']['sys_email'])) {
        return $_SESSION['auth_user']['sys_email'];
    } elseif (isset($_SESSION['auth_user']['uid'])) {
        return 'user_' . $_SESSION['auth_user']['uid'];
    } else {
        return 'system';
    }
}

/**
 * Helper function to safely get store ID from request
 *
 * @return int The store ID or 0 if not available
 */
function getStoreIdFromRequest() {
    if (isset($_GET['storeID'])) {
        return intval($_GET['storeID']);
    } elseif (isset($_POST['storeID'])) {
        return intval($_POST['storeID']);
    } else {
        return 0;
    }
}

/**
 * Log product management audit events (create, update, delete)
 *
 * @param mysqli $conn Database connection
 * @param int $store_id Store ID for multi-tenant security
 * @param string $action Action performed (created, updated, deleted)
 * @param int $product_id Product ID being affected
 * @param string $product_name Product name
 * @param string $product_code Product barcode/code (optional)
 * @param string $log_user User performing the action (optional, will use getCurrentUser() if not provided)
 * @return bool Success status
 */
function logProductAudit($conn, $store_id, $action, $product_id, $product_name, $product_code = '', $log_user = null) {
    try {
        // Get current user if not provided
        if ($log_user === null) {
            $log_user = getCurrentUser();
        }

        // Determine page title based on action
        $page_titles = [
            'created' => 'Product Management - Add Product',
            'updated' => 'Product Management - Update Product',
            'deleted' => 'Product Management - Delete Product'
        ];
        $page_title = $page_titles[$action] ?? 'Product Management - ' . ucfirst($action);

        $audit_sql = "INSERT INTO logs_audit (store_id, log_type, page_title, column_barcode, column_item, log_user, log_datetime)
                      VALUES (?, ?, ?, ?, ?, ?, NOW())";

        $audit_stmt = $conn->prepare($audit_sql);
        if (!$audit_stmt) {
            error_log("Failed to prepare product audit statement: " . $conn->error);
            return false;
        }

        // Prepare variables for bind_param (required for PHP strict mode)
        $log_type = 'product_management';
        $barcode = $product_code ?: 'no_barcode';

        $audit_stmt->bind_param(
            'isssss',
            $store_id,
            $log_type,
            $page_title,
            $barcode,
            $product_name,
            $log_user
        );

        if ($audit_stmt->execute()) {
            $audit_stmt->close();
            return true;
        } else {
            error_log("Failed to execute product audit statement: " . $audit_stmt->error);
            $audit_stmt->close();
            return false;
        }
    } catch (Exception $e) {
        error_log("Exception in logProductAudit: " . $e->getMessage());
        return false;
    }
}
?>
